<template>
  <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
    <view class="navbar-content">
      <view class="navbar-left" @click="handleBack" v-if="showBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="navbar-center">
        <text class="navbar-title">{{ title }}</text>
      </view>
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { getStatusBarHeight } from '@/utils/tools'

defineProps({
  title: { type: String, default: '' },
  showBack: { type: Boolean, default: true }
})

const emit = defineEmits(['back'])
const statusBarHeight = getStatusBarHeight()

const handleBack = () => {
  emit('back')
  uni.navigateBack()
}
</script>

<style scoped lang="scss">
.custom-navbar {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;

  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.back-icon {
  font-size: 40rpx;
  color: #333333;
  font-weight: bold;
}

.navbar-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 400rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.navbar-right {
  display: flex;
  align-items: center;
  min-width: 80rpx;
  height: 60rpx;
}
</style>